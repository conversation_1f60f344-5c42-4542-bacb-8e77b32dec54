import { Injectable } from '@angular/core';
import { SOMETHING_GONE_WRONG } from '@constants';
import { AlertDialogComponent } from '@shared/components/dialogs/alert-dialog/alert-dialog.component';
import { MatDialog } from '@angular/material/dialog';

@Injectable()
export class HttpStatusesHandlerService {

    constructor(
        private readonly dialog: MatDialog,
    ) {
    }

    public handleSchedulesCode(error: { status: number } | undefined): void {
        const status = error && error.status;
        let message = '';
        let isAlertSkipped = false;

        switch (status) {
            case 503:
                message = 'Our connection to HM Land Registry is currently unavailable. Please try again during working hours.';
                break;
            case 400:
                message = 'Something\'s gone wrong. Please clear your temporary internet files and try again.';
                break;
            case 401:
            case 403:
                message = 'Your account doesn\'t seem to be authorised to do this. Please try again or contact our support live chat.';
                break;
            case 402:
                message = 'You\'ve run out of credit. Please contact your head of innovation or LegalTech for more information.';
                break;
            case 409:
                message = 'Another transaction of this project is in progress. '
                    + 'Please wait until the other transaction is complete to avoid purchasing the same items twice.';
                break;
            case 416:
                isAlertSkipped = true;
                break;
            default:
                message = SOMETHING_GONE_WRONG.message;
                break;
        }

        if (isAlertSkipped) {
            return;
        }

        this.dialog.open(AlertDialogComponent, {
            panelClass: 'report-dialog',
            width: '400px',
            data: {
                title: 'Oops!',
                message,
            },
        });
    }
}
