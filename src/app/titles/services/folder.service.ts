import { Injectable } from '@angular/core';

import { FolderApi } from '../api';

import { MapSearchStore } from '../store';

import { Observable } from 'rxjs';
import { ImanageParams } from '../types';
import { ProfileService } from '@services';
import { ProjectsService } from '../../project-details/services/projects.service';
import { ProjectDetailsQuery } from '../../project-details/stores/project-details/project-details.query';

@Injectable()
export class FolderService {

    constructor(
        private readonly folderApi: FolderApi,
        private readonly profileService: ProfileService,
        private readonly mapSearchStore: MapSearchStore,
        private readonly projectsService: ProjectsService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
    ) {
    }

    public async createFolder(): Promise<string> {
        const project = await this.projectsService.createProject();
        this.mapSearchStore.reset();

        return project.id;
    }

    public isDetailsFiled(): boolean {
        const isHmlrEnabled = this.profileService.isHmlrEnabled$.getValue();

        return !isHmlrEnabled
            ? true
            : this.projectDetailsQuery.getIsDetailsExist();
    }


    // Login to Imanage
    public logInToImanage(data: any, agentUrl: string | undefined = '', agentSecret: string | undefined = ''): Observable<any> {
        return this.folderApi.logInToImanage(data, agentUrl, agentSecret);
    }

    public checkIfAuthorised(): Observable<any> {
        return this.folderApi.checkIfAuthorised();
    }

    public getDialogTokenCall(agentUrl: string, agentSecret: string, authorization: string): Observable<any> {
        return this.folderApi.getDialogTokenCall(agentUrl, agentSecret, authorization);
    }

    public downloadFiles(folderId: string, confData: ImanageParams, data: any): Observable<any> {
        return this.folderApi.downloadFiles(folderId, confData, data);
    }

    public isDownloadFinished(confData: ImanageParams, taskId: string): Observable<any> {
        return this.folderApi.isDownloadFinished(confData, taskId);
    }

    public downloadFilesToUi(confData: ImanageParams, contentLocation: string): Observable<any> {
        return this.folderApi.downloadFilesToUi(confData, contentLocation);
    }

    public sendThemToAvail(folderId: string, formData: any): Observable<any> {
        return this.folderApi.sendThemToAvail(folderId, formData);
    }

    public sendImanageDataToAvail(folderId: string, data: any): Observable<any> {
        return this.folderApi.sendImanageDataToAvail(folderId, data);
    }

    public checkImanageConfiguration(): Observable<any> {
        return this.folderApi.checkImanageConfiguration();
    }
}
