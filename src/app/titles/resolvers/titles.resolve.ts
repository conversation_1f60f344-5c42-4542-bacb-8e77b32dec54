import { NEVER, Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, Router } from '@angular/router';
import { catchError } from 'rxjs/operators';

import { DocumentsService } from '../services';
import { IResponseStatus } from '@core/types';
import { IDocument } from '../types';
import { ProjectDetailsQuery } from '../../project-details/stores/project-details/project-details.query';
import { ProjectsService } from '../../project-details/services/projects.service';
import { LoggerService } from '@services';


@Injectable()
export class TitlesResolve implements Resolve<IResponseStatus<IDocument>> {

    constructor(
        private readonly documentsService: DocumentsService,
        private readonly router: Router,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly projectsService: ProjectsService,
        private readonly log: LoggerService,
    ) {
    }

    public resolve(route: ActivatedRouteSnapshot): Observable<IResponseStatus<IDocument>> {
        const id = route.queryParams.fid || this.projectDetailsQuery.projectId;
        if (id) {
            return this.documentsService.loadFolderDocuments(id)
                .pipe(
                    catchError((error) => {
                        this.log.error('TitlesResolve error:', error);
                        this.projectsService.reset();
                        void this.router.navigate(['title']);

                        return NEVER;
                    }),
                );
        } else {
            this.log.info('No folder id, no need to load documents');
        }
    }
}
