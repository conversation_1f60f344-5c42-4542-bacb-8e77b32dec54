import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpErrorResponse } from '@angular/common/http';

import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { filter, first, take, takeUntil, tap } from 'rxjs/operators';

import { DocumentsQuery, FailedDocumentsQuery, FailedPurchasesQuery } from '../../store';
import { DocumentsService, FolderService, ImanageService, RefreshService, ReportGenerationHandlerService } from '../../services';

import {
    MISMATCH_TITLES_AND_PLANS,
    NO_TITLES_ONLY_PLANS,
    NO_VALID_DOCUMENT_TO_PROCEED,
    REFRESH_HM_CONFIRM_DATA,
    REFRESH_SCOTLAND_CONFIRM_DATA,
    SOMETHING_GONE_WRONG,
    UNABLE_COMMUNICATE_WITH_SERVER,
} from '@constants';

import { IFailedDocument, IFailedPurchase } from '@core/types';
import { IDocument, LandRegistryDialogOptions, SortType } from '../../types';

import { ConfirmDialogComponent, IConfirmDialogData } from '@shared/components/dialogs/confirm-dialog/confirm-dialog.component';
import { LandRegistryDialogComponent } from '../dialogs/land-registry-dialog/land-registry-dialog.component';
import { LoggerService, PlanService, ProfileService } from '@services';
import { environment } from '@env/environment';
import { OnboardingManageService } from '../../../onboarding/services';
import { AlertOkDialogComponent } from '@shared/components/dialogs/alert-ok-dialog/alert-ok-dialog.component';
import { LngLat } from 'maplibre-gl';
import { OrderType, RegistrySearchType } from '@enums';
import { SearchRegistry } from '../../enums/search-register.enum';
import { LandRegistryDialogQuery, LandRegistryDialogService } from '../../store/land-registry-dialog';
import { hmLandRegistryDocuments, scotlandRegistryDocuments } from '../../constants/land-registry-search.constants';
import { titleProjectDetailsOptions } from '../../constants/title-project-details-dialog-options.constant';
import { ProjectDetailsDialogService } from '../../../project-details/services/project-details-dialog.service';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';

@Component({
    selector: 'avl-titles-upload',
    templateUrl: './titles-upload.component.html',
    styleUrls: ['./titles-upload.component.scss'],
})
export class TitlesUploadComponent implements OnInit, OnDestroy {
    public readonly isDragDropEnabled = environment.isTitleDocumentsReorderingEnabled;
    public readonly resetUploadAreaState$ = new Subject<void>();
    public searchRegistry$: Observable<SearchRegistry>;
    public failedDocuments$: Observable<IFailedDocument[]>;
    public failedPurchases$: Observable<IFailedPurchase[]>;
    public landRegistryButtonDisabled = false;
    public isDisabledBtn = false;
    public isDocumentLoading = true;
    public folderId$: Observable<string>;
    public succeedDocuments: IDocument[] = [];
    public sortType: SortType;
    public orderType: OrderType;
    public hasTitles = new BehaviorSubject<boolean>(false);
    public imanageButtonDisabled = false;
    public showImanageIframe = false;
    public isOldTheme = true;
    public isNextButtonThrottleCooldown = false;

    private readonly destroy$ = new Subject<void>();
    private succeedDocuments$: Observable<IDocument[]>;

    constructor(
        private readonly documentsQuery: DocumentsQuery,
        private readonly refreshService: RefreshService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly folderService: FolderService,
        private readonly failedDocumentsQuery: FailedDocumentsQuery,
        private readonly failedPurchasesQuery: FailedPurchasesQuery,
        private readonly documentsService: DocumentsService,
        private readonly planService: PlanService,
        private readonly dialog: MatDialog,
        private readonly router: Router,
        private readonly activatedRoute: ActivatedRoute,
        private readonly log: LoggerService,
        private readonly handlerService: ReportGenerationHandlerService,
        private readonly onboarding: OnboardingManageService,
        private readonly imanageService: ImanageService,
        private readonly landRegistryQuery: LandRegistryDialogQuery,
        private readonly landRegistryService: LandRegistryDialogService,
        private readonly profileService: ProfileService,
        private readonly projectDetailsDialogService: ProjectDetailsDialogService,
    ) {
    }

    public ngOnInit(): void {
        this.searchRegistry$ = this.landRegistryQuery.select('registry');
        this.isOldTheme = this.activatedRoute.snapshot.data['isOldTheme'];
        this.documentsService.orderType
            .pipe(first())
            .subscribe((type) => this.orderType = type);
        this.documentsService.sortType
            .pipe(first())
            .subscribe((type) => this.sortType = type);
        this.folderId$ = this.projectDetailsQuery.selectProjectId();
        this.documentsQuery.selectIsDocumentLoading()
            .pipe(takeUntil(this.destroy$))
            .subscribe((isLoading) => this.isDocumentLoading = isLoading);
        this.documentsQuery.selectLoadingStopped()
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => this.resetUploadAreaState$.next());
        this.succeedDocuments$ = this.documentsQuery.selectAll();
        this.failedDocuments$ = this.failedDocumentsQuery.selectAll();
        this.failedPurchases$ = this.failedPurchasesQuery.selectAll();
        this.setupListeners();
        this.setOnboarding();
        this.listenToOnboardingEvents();
        this.setOnImanageLoad();
        this.checkUrlParams();

        const isAllProcessed = this.documentsQuery.getIsAllProcessed();
        if (!isAllProcessed) {
            this.documentsService.refreshDocuments().subscribe();
        }
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    public setupListeners(): void {
        this.succeedDocuments$
            .pipe(takeUntil(this.destroy$))
            .subscribe((docs: IDocument[]) => {
                this.succeedDocuments = docs;
                const documentTypes = Array.from(new Set(docs.map((item: any) => item.type)));
                this.handlerService.setUniqueDocTypes(documentTypes);
                const validDocumentTypes = documentTypes.filter((d) => d.type !== 'unknown');
                this.hasTitles.next(validDocumentTypes.length > 0);
            });

        this.activatedRoute.queryParams
            .pipe(take(1))
            .subscribe((params) => {
                if ('download' in params) {
                    this.handlerService.openGenerateReportDialog();
                }
            });

        this.requestProjectRefresh();
    }

    public onLandRegistryOpened(): void {
        this.landRegistryButtonDisabled = true;
        this.projectDetailsQuery.isProjectCreated
            ? this.proceedProjectDetailsOrLandRegistryDialog(false)
            : this.startNewProject({});
    }

    public onFailedDocumentRemove(id: string): void {
        this.documentsService.removeFailedDocument(id)
            .pipe(take(1))
            .subscribe({
                error: (err: HttpErrorResponse) => {
                    if (err.status === 404) {
                        this.documentsService.removeFailedDocumentFromStore(id);
                    }
                },
            });
    }

    public onFailedPurchaseRemove(id: string): void {
        this.log.info('remove the failed purchase', id);
        this.documentsService.removeFailedPurchaseAlert(id);
    }

    public navigateToNewFolder(folderId: string): void {
        this.router.navigate([], {
            relativeTo: this.activatedRoute,
            queryParams: { fid: folderId },
            queryParamsHandling: 'merge',
        });
    }

    public onNextButtonClick(): void {
        this.checkRelatedDocumentsAndProceed();
        this.isNextButtonThrottleCooldown = true;
        setTimeout(() => this.isNextButtonThrottleCooldown = false, 1000);
    }

    public proceedToNextStep(): void {
        this.planService.isSchedulesEnabled(this.projectDetailsQuery.projectId)
            .pipe(take(1))
            .subscribe((enabled) => {
                if (enabled) {
                    this.onboarding.closeActiveOverlay();
                    this.router.navigate(
                        ['/title/schedules'],
                        {
                            queryParams: {
                                fid: this.projectDetailsQuery.projectId,
                            },
                        },
                    );
                } else {
                    this.handlerService.openGenerateReportDialog();
                }
            });
    }


    // ************* Imanage button event ****************************
    public onImanageOpened(): void {
        this.imanageButtonDisabled = true;
        this.projectDetailsQuery.isProjectCreated
            ? this.checkImanageLogin()
            : this.startNewProject({ isImanageTrigger: true });
    }

    // Function to handle the login process of Imanage
    public showImanageDialog(): void {
        this.log.info('titles.agentAuthorization');
        this.showImanageIframe = false;
        this.imanageService.showImanageDialog();
        this.imanageService.showDirectIframe$
            .pipe(takeUntil(this.destroy$))
            .pipe(filter((d) => d.showIframe === true))
            .pipe(take(1))
            .subscribe((resp) => {
                // this.unsubscribe$.unsubscribe();
                if (resp.showIframe === true) {
                    this.showImanageIframe = true;
                }
            });
    }

    // Check display of Imanage iframe
    public setOnImanageLoad(): void {
        this.activatedRoute.queryParamMap
            .subscribe((params) => {
                const codeParam = params.get('code');
                if (codeParam !== null) {
                    this.showImanageIframe = true;
                }
            });
    }

    public showRegistrySearchDialog(type: RegistrySearchType): void {
        this.landRegistryButtonDisabled = true;
        const landRegistryOptions: LandRegistryDialogOptions = {
            searchType: type,
        };

        this.projectDetailsQuery.isProjectCreated
            ? this.proceedProjectDetailsOrLandRegistryDialog(false, landRegistryOptions)
            : this.startNewProject({ landRegistryOptions });
    }

    public changeSearchRegistry(registry: SearchRegistry): void {
        this.landRegistryService.changeSearchRegistry(registry);
    }

    private proceedProjectDetailsOrLandRegistryDialog(
        isImanageTrigger: boolean | undefined = false,
        options?: LandRegistryDialogOptions,
    ): void {
        if (isImanageTrigger) {
            this.showProjectDetailsDialog();
        } else {
            this.openLandRegistryDialog(options);
        }
    }

    private showProjectDetailsDialog(): void {
        const options = {
            ...titleProjectDetailsOptions,
            isCloseDisabled: this.onboarding.isActive,
        };
        this.projectDetailsDialogService.show(options)
            .then((result) => {
                if (result.isClosedByUser) {
                    return;
                }

                this.checkImanageLogin();
            })
            .catch((error) => {
                this.log.error(error);
                this.handlerService.openAlertDialog(false, SOMETHING_GONE_WRONG);
            })
            .finally(() => {
                this.landRegistryButtonDisabled = false;
                this.imanageButtonDisabled = false;
            });
    }

    private startNewProject({
        isImanageTrigger = false,
        landRegistryOptions,
    }: {
        isImanageTrigger?: boolean;
        landRegistryOptions?: LandRegistryDialogOptions;
    }): void {
        this.folderService.createFolder()
            .then((folderId) => {
                this.navigateToNewFolder(folderId);
                this.proceedProjectDetailsOrLandRegistryDialog(isImanageTrigger, landRegistryOptions);
            })
            .catch(() => {
                this.handlerService.openAlertDialog(false, UNABLE_COMMUNICATE_WITH_SERVER);
                if (isImanageTrigger) {
                    this.imanageButtonDisabled = false;
                } else {
                    this.landRegistryButtonDisabled = false;
                }
            });
    }

    private checkUrlParams(): void {
        const paramsMap = this.activatedRoute.snapshot.queryParamMap;
        const landRegistryType = paramsMap.get('searchRegistry');
        const landRegistrySearchType = paramsMap.get('searchType');
        const titleNumber = paramsMap.get('titleNumber');
        const mapIsFreeholdsOn = paramsMap.get('isFreeholdsOn');
        const mapIsLeaseholdsOn = paramsMap.get('isLeaseholdsOn');
        const filters = {
            isFreeholdsOn: mapIsFreeholdsOn === 'true',
            isLeaseholdsOn: mapIsLeaseholdsOn === 'true',
        };
        const mapZoom = paramsMap.get('zoom');
        const mapLat = paramsMap.get('lat');
        const mapLng = paramsMap.get('lng');
        const location = new LngLat(Number(mapLng), Number(mapLat));
        const isFiltersConfigured = mapIsFreeholdsOn !== null || mapIsLeaseholdsOn !== null;
        const isLocationConfigured = mapLat !== null || mapLng !== null;
        const isMapSearchEnabled = this.profileService.isMapSearchEnabled$.getValue();

        if (landRegistrySearchType) {
            const validatedSearchType = landRegistrySearchType === RegistrySearchType.map && !isMapSearchEnabled
                ? RegistrySearchType.titleNumber
                : landRegistrySearchType;
            const landRegistryOptions: LandRegistryDialogOptions = {
                zoom: mapZoom && Number(mapZoom),
                location: isLocationConfigured ? location : null,
                filters: isFiltersConfigured ? filters : null,
                titleNumber,
                searchType: validatedSearchType as RegistrySearchType,
                withRefresh: false,
                searchRegistry: landRegistryType as SearchRegistry,
            };

            this.openLandRegistryDialog(landRegistryOptions);
        }
    }

    private openLandRegistryDialog(landRegistryOptions?: LandRegistryDialogOptions): void {
        const newRegistry = landRegistryOptions.searchRegistry;
        if (newRegistry) {
            this.changeSearchRegistry(newRegistry);
        }

        const dialogRef = this.dialog.open(LandRegistryDialogComponent, {
            panelClass: 'land-registry-dialog',
            data: landRegistryOptions || { withRefresh: false },
        });

        this.afterClosedLandRegistry(dialogRef);
    }

    private afterClosedLandRegistry(dialogRef: MatDialogRef<LandRegistryDialogComponent>): void {
        dialogRef
            .afterClosed()
            .pipe(
                tap((result) => {
                    if (result) {
                        const dialogDetails = {
                            title: result.title,
                            message: result.message,
                        };

                        if (result.result === 'success') {
                            this.handlerService.openDoneDialog(dialogDetails);
                        } else if (result.result === 'error') {
                            this.handlerService.openAlertDialog(false, dialogDetails);
                        }
                    }

                    this.landRegistryButtonDisabled = false;
                    this.imanageButtonDisabled = false;
                    this.onboarding.destroyOnboarding();
                }),
            )
            .subscribe();
    }

    private setOnboarding(): void {
        if (this.onboarding.isActive) {
            this.onboarding.isShowReport = true;
            this.onboarding.isDisabledNextButton$.next(true);
        }
    }

    private listenToOnboardingEvents(): void {
        this.onboarding.isDisabledNextButton$
            .pipe(takeUntil(this.destroy$))
            .subscribe((isDisabled) => {
                this.isDisabledBtn = this.onboarding.isActive && isDisabled;
            });
        this.onboarding.isOnboardingActive()
            .pipe(
                takeUntil(this.destroy$),
                filter((isActive) => isActive),
            )
            .subscribe(() => {
                this.setOnboarding();
            });
    }

    private checkRelatedDocuments(): string {
        const titleRegisters = this.succeedDocuments.filter((d) => d.type === 'title-register').map((d) => d.titleNumber);
        const titlePlans = this.succeedDocuments.filter((d) => d.type === 'title-plan').map((d) => d.titleNumber);
        const scottishTitles = this.succeedDocuments.filter((d) => d.type === 'scottish-title').map((d) => d.titleNumber);
        if (titlePlans.length === 0) {
            if (titleRegisters.length === 0 && scottishTitles.length === 0) {
                return 'has-no-title-nor-plans';
            }
            return 'Okay';
        }
        if (titleRegisters.length === 0) {
            if (scottishTitles.length === 0) {
                return 'has-no-title-only-plans';
            }
            return 'has-title-mismatched-plans';
        }
        for (const titleNumber of titlePlans) {
            if (!titleRegisters.includes(titleNumber)) {
                return 'has-title-mismatched-plans';
            }
        }
        return 'Okay';
    }

    private checkRelatedDocumentsAndProceed(): void {
        const state = this.checkRelatedDocuments();
        if (state === 'has-title-mismatched-plans') {
            const dialogRef = this.dialog.open(
                ConfirmDialogComponent,
                {
                    panelClass: 'confirm-dialog',
                    data: MISMATCH_TITLES_AND_PLANS,
                    maxWidth: '400px',
                },
            );

            dialogRef.afterClosed().subscribe((isConfirm) => {
                if (isConfirm) {
                    this.proceedToNextStep();
                }
            });
        } else if (state === 'has-no-title-only-plans') {
            this.dialog.open(AlertOkDialogComponent, NO_TITLES_ONLY_PLANS).afterClosed();
        } else if (state === 'has-no-title-nor-plans') {
            this.dialog.open(AlertOkDialogComponent, NO_VALID_DOCUMENT_TO_PROCEED).afterClosed();
        } else {
            this.proceedToNextStep();
        }
    }

    private checkImanageLogin(): void {
        const isDetailsFiled = this.folderService.isDetailsFiled();

        if (isDetailsFiled) {
            this.showImanageDialog();
        } else {
            this.showProjectDetailsDialog();
        }

        this.imanageButtonDisabled = false;
    }

    private requestProjectRefresh(): void {
        const refreshHMDocuments = (): void => {
            this.refreshService.onRefresh()
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                    next: (refreshed) => refreshed
                        && this.showConfirmRefreshDocumentsDialog(SearchRegistry.hmlr, REFRESH_HM_CONFIRM_DATA),
                    complete: () => this.refreshService.resetRefresh(),
                });
        };
        const refreshScotLisDocuments = (): void => {
            this.refreshService.onRefresh()
                .pipe(takeUntil(this.destroy$))
                .subscribe({
                    next: (refreshed) => refreshed
                        && this.showConfirmRefreshDocumentsDialog(SearchRegistry.ros, REFRESH_SCOTLAND_CONFIRM_DATA),
                    complete: () => this.refreshService.resetRefresh(),
                });
        };
        this.chooseProjectType(refreshHMDocuments, refreshScotLisDocuments);
    }

    private showConfirmRefreshDocumentsDialog(register: SearchRegistry, dialogData: IConfirmDialogData): void {
        this.dialog.open(ConfirmDialogComponent, {
            panelClass: 'confirm-dialog',
            data: dialogData,
        })
            .afterClosed()
            .subscribe((isConfirm) => {
                if (isConfirm) {
                    this.openLandRegistryDialog({
                        withRefresh: true,
                        searchRegistry: register,
                        noDataDisclaimer: 'All your titles are up to date',
                    });
                }
            });
    }

    private chooseProjectType(hmCallback: () => void, scotLisCallback: () => void): void {
        const isFolderExist = this.projectDetailsQuery.isProjectCreated;
        const isFolderEmpty = this.documentsQuery.isEmpty();

        if (!isFolderExist || isFolderEmpty) {
            return;
        }

        const documentTypes = this.documentsQuery.getAll().map((doc) => doc.type);
        const isScotLisDocumentExist = documentTypes.some((docType) => scotlandRegistryDocuments.includes(docType));
        const isHMDocumentExist = documentTypes.some((docType) => hmLandRegistryDocuments.includes(docType));

        if (isScotLisDocumentExist && isHMDocumentExist) {
            return;
        }

        if (isScotLisDocumentExist) {
            return scotLisCallback();
        }

        if (isHMDocumentExist) {
            return hmCallback();
        }
    }
}
