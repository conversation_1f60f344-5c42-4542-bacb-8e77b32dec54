import { Injectable } from '@angular/core';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { DocumentsSortToMap } from '../types';

@Injectable()
export class DocumentApi {

    constructor(
        private readonly http: HttpClient,
    ) {
    }

    public createUploadLink(folderId: string): string {
        return `/api/titles/document/${folderId}`;
    }

    public getDocumentBlob(folderId: string, documentId: string): Observable<HttpResponse<Blob>> {
        const url = this.getTitlesDocumentUrl(folderId, documentId);

        return this.http.get(url, { responseType: 'blob', observe: 'response' });
    }

    public getTitlesDocumentUrl(folderId: string, documentId: string): string {
        return `/api/titles/document/${folderId}/${documentId}`;
    }

    public removeDocument(folderId: string, documentId: string): Observable<void> {
        return this.http.delete<void>(this.getTitlesDocumentUrl(folderId, documentId));
    }

    public sort(folderId: string, sort: DocumentsSortToMap): Observable<void> {
        return this.http.post<void>(`/api/titles/folder/${folderId}/document-sort`, sort);
    }

}
