import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { BrowserModule } from '@angular/platform-browser';
import { APP_INITIALIZER, Error<PERSON>andler, NgModule, Provider } from '@angular/core';
import { Router, RouterModule } from '@angular/router';

import { HTTP_INTERCEPTORS, HttpClientJsonpModule, HttpClientModule } from '@angular/common/http';
import { AngularFireAuthModule } from '@angular/fire/compat/auth';
import { AngularFireModule } from '@angular/fire/compat';

import { AkitaNgDevtools } from '@datorama/akita-ngdevtools';
import * as Sentry from '@sentry/angular-ivy';
import { AppComponent } from './app.component';

import { CoreModule } from './core/core.module';
import { SharedModule } from '@shared/shared.module';
import { AuthModule } from '@auth/auth.module';

import { AuthRoutingModule } from '@auth/routing/auth.routing.module';

import { IntercomModule } from 'ng-intercom';

import { CachControlInterceptor } from './core/interceptors/cash-control.interceptor';
import { TokenInterceptor } from './core/interceptors/token.interceptor';
import { LoggingInterceptor } from './core/interceptors/logging.interceptor';
import { AuthorisationInterceptor } from './core/interceptors/authorisation.interceptor';

import { environment } from '@env/environment';
import { appRoutes } from './app-routing';
import { AppErrorHandler } from './app.error-handler';
import { SystemInfoModule } from './system-info/system-info.module';
import player, { LottiePlayer } from 'lottie-web';
import { LottieModule } from 'ngx-lottie';
import { ProjectDetailsModule } from './project-details/project-details.module';

const providersList: Provider[] = [
    { provide: ErrorHandler, useClass: AppErrorHandler },
    { provide: HTTP_INTERCEPTORS, useClass: TokenInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: CachControlInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: AuthorisationInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: LoggingInterceptor, multi: true },
];

if (environment.sentryData.isTracingEnabled) {
    providersList.push({ provide: Sentry.TraceService, deps: [Router] });
    providersList.push({
        provide: APP_INITIALIZER,
        useFactory: () => () => void 0,
        deps: [Sentry.TraceService],
        multi: true,
    });
}

export function playerFactory(): LottiePlayer {
    return player;
}

@NgModule({
    declarations: [
        AppComponent,
    ],
    imports: [
        BrowserModule,
        BrowserAnimationsModule,
        environment.production ? [] : AkitaNgDevtools.forRoot(),
        AuthModule,
        SharedModule,
        CoreModule,
        RouterModule.forRoot(appRoutes),
        AuthRoutingModule,
        AngularFireModule.initializeApp(environment.firebase),
        AngularFireAuthModule,
        IntercomModule.forRoot(environment.intercomModule),
        SystemInfoModule,
        LottieModule.forRoot({ player: playerFactory }),
        HttpClientModule,
        HttpClientJsonpModule,
        ProjectDetailsModule,
    ],
    providers: providersList,
    bootstrap: [AppComponent],
})
export class AppModule {
}
