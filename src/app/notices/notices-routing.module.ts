import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NoticesComponent } from './notices.component';
import { NoticesResolver } from './resolvers/notices.resolver';
import { NoticeDocumentsComponent } from './components/notice-documents/notice-documents.component';
import { PreviousFoldersComponent } from './components/previous-folders/previous-folders.component';
import { NotFoundPageComponent } from '@shared/components/not-found-page/not-found-page.component';
import { DocumentViewerComponent } from '@shared/components/document-viewer/document-viewer.component';
import { ProjectDetailsResolver } from '../project-details/resolvers/project-details.resolver';


const routes: Routes = [
    {
        path: '',
        component: NoticesComponent,
        children: [
            {
                path: '',
                redirectTo: 'upload',
                pathMatch: 'full',
            },
            {
                path: 'upload',
                component: NoticeDocumentsComponent,
                resolve: {
                    project: ProjectDetailsResolver,
                    noticeDocuments: NoticesResolver,
                },
            },
            {
                path: 'previous-projects',
                component: PreviousFoldersComponent,
            },
        ],
    },
    {
        path: 'documents',
        children: [
            {
                path: 'not-found',
                component: NotFoundPageComponent,
                data: {
                    logoUrl: '/assets/images/avail-notice-logo.svg',
                    title: 'Document Not Found',
                    description: 'This document has been deleted or moved.<br>Click Back to Project to return to your project or the home page.<br>Alternatively, select Message Us to contact our support team for assistance.',
                    backButtonText: 'Back to Project',
                },
            },
            {
                path: ':documentId',
                data: {
                    resourceUrlWithPlaceholders: 'api/notice/folder/:folderId/document/:documentId/preview',
                    resourceNotFoundPage: 'notice/documents/not-found',
                    folderPageUrl: '/notice/upload?fid=:folderId',
                },
                component: DocumentViewerComponent,
            },
        ],
    },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class NoticesRoutingModule {
}
