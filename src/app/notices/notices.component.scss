@import '~assets/sass/mixins';
@import '~assets/sass/variables';

:host {
    display: block;
    height: 100%;
}

.notice-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0 $folder-xl-padding 40px;
    margin: 0 auto;

    @include maxW(992px) {
        padding-right: $folder-lg-padding;
        padding-left: $folder-lg-padding;
    }

    @include maxW(768px) {
        padding-right: $folder-md-padding;
        padding-left: $folder-md-padding;
    }

    @include maxH(700px) {
        padding-bottom: 20px;
    }
}

.sidenav {
    height: 100%;
}
