import { Injectable } from '@angular/core';
import { HttpResponse } from '@angular/common/http';
import { concatMap, map, switchMap, tap } from 'rxjs/operators';
import { NoticeApi } from '../api/notice.api';
import { NoticeDocumentsQuery, NoticeDocumentsStore } from '../store/notice-documents';
import { createEmptyClientNoticeDocument, INoticeDocument } from '../types';
import { Observable, Subscription, timer } from 'rxjs';
import { IResponseStatus } from '@core/types';
import { NoticeDocType } from '../enums';
import { MatDialog } from '@angular/material/dialog';
import { AlertDialogComponent } from '@shared/components/dialogs/alert-dialog/alert-dialog.component';
import { LoggerService, UrlParamsService } from '@services';
import { ProjectDetailsDialogService } from '../../project-details/services/project-details-dialog.service';
import { noticeProjectDetailsOptions } from '../constants/notice-project-details-dialog-options.constant';
import { ProjectDetailsQuery } from '../../project-details/stores/project-details/project-details.query';
import { BookmarksStore } from '../store/bookmarks/bookmarks.store';

@Injectable()
export class NoticeService {
    private readonly statusPollingIntervalMs = 3000;
    private readonly reportGenerationPollingIntervalMs = 3000;
    private statusPoll$: Observable<IResponseStatus<INoticeDocument>> = null;
    private statusPollSubscription: Subscription = null;

    constructor(
        private readonly noticeDocumentsQuery: NoticeDocumentsQuery,
        private readonly noticeDocumentsStore: NoticeDocumentsStore,
        private readonly api: NoticeApi,
        private readonly dialog: MatDialog,
        private readonly urlParamsService: UrlParamsService,
        private readonly projectDetailsDialogService: ProjectDetailsDialogService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly bookmarksStore: BookmarksStore,
        private readonly log: LoggerService,
    ) {
    }

    public restoreFolderIdUrlQueryParam(): void {
        const folderId = this.projectDetailsQuery.projectId;

        if (!folderId) {
            return;
        }

        this.urlParamsService.addParams({ fid: folderId });
    }

    public openProjectDetailsDialogWithData(): void {
        const options = {
            ...noticeProjectDetailsOptions,
            data: this.projectDetailsQuery.getDetails(),
        };

        void this.projectDetailsDialogService.show(options)
            .catch((error) => this.log.error(error));
    }

    public bindUploadIdWithFileId(uploadId: string, documentId: string): void {
        this.noticeDocumentsStore.update((doc) => doc.id === uploadId, { id: documentId });
    }

    public startPolling(): void {
        if (!this.statusPoll$) {
            const folderId = this.projectDetailsQuery.projectId;

            this.statusPoll$ = timer(this.statusPollingIntervalMs, this.statusPollingIntervalMs)
                .pipe(
                    concatMap(() => this.api.getFolderStatus(folderId)),
                    tap((response) => {
                        const documents = response.documents;

                        this.syncDocumentsWithState(documents);
                        this.checkIsAllDocumentsUploaded();
                    }),
                );
            this.statusPollSubscription = this.statusPoll$.subscribe();
        }
    }

    public stopPolling(): void {
        this.statusPoll$ = null;
        this.statusPollSubscription?.unsubscribe();
    }

    public loadFolderStatus(projectId: string): Observable<INoticeDocument[]> {
        return this.api.getFolderStatus(projectId)
            .pipe(
                map((response) => {
                    const documents = response.documents;

                    if (documents.length) {
                        this.noticeDocumentsStore.upsertMany(documents);
                    } else {
                        this.noticeDocumentsStore.set([]);
                    }

                    this.checkIsAllDocumentsUploaded();

                    return documents;
                }),
            );
    }

    public clearState(): void {
        this.clearDocumentsStore();
        this.bookmarksStore.reset();
    }

    public clearDocumentsStore(): void {
        this.noticeDocumentsStore.reset();
    }

    public generateNotice(folderId: string, noticeType: string): Observable<string> {
        return this.api.generateNotice(folderId, noticeType)
            .pipe(
                map((response) => response.headers.get('Content-Location')),
            );
    }

    public getGenerationStatus(url: string): Observable<HttpResponse<void>> {
        return timer(this.reportGenerationPollingIntervalMs, this.reportGenerationPollingIntervalMs)
            .pipe(
                switchMap(() => this.api.getGenerationStatus(url)),
            );
    }

    public removeDocument(folderId: string, documentId: string): Observable<void> {
        return this.api.removeDocument(folderId, documentId)
            .pipe(
                tap(() => this.noticeDocumentsStore.remove(documentId)),
            );
    }

    public checkIsAllDocumentsUploaded(): void {
        const isUploadingFinished = this.noticeDocumentsQuery.isEveryProcessed();

        this.setIsLoading(!isUploadingFinished);

        if (isUploadingFinished) {
            this.stopPolling();
        }
    }

    public setIsLoading(isLoading: boolean): void {
        this.noticeDocumentsStore.setLoading(isLoading);
    }

    public addTemporaryDocument(id: string, type: NoticeDocType, fileName: string): void {
        const tempDocument = this.createTemporaryDocument(id, type, fileName);
        this.noticeDocumentsStore.add(tempDocument);
    }

    public openAlertDialog(errorData?: { title: string; message: string }): void {
        this.dialog.open(AlertDialogComponent, {
            panelClass: 'report-dialog',
            width: '400px',
            data: errorData,
        });
    }

    private createTemporaryDocument(id: string, type: NoticeDocType, fileName: string): INoticeDocument {
        const emptyDocument = createEmptyClientNoticeDocument();
        emptyDocument.id = id;
        emptyDocument.clientType = type;
        emptyDocument.fileName = fileName;

        return emptyDocument;
    }

    private syncDocumentsWithState(documents: INoticeDocument[]): void {
        documents.forEach((uploadedDocument) => {
            this.noticeDocumentsStore.update(
                (doc) => doc.id === uploadedDocument.id,
                { ...uploadedDocument },
            );
        });
    }
}
