:host {
    flex: 1;
}

.previous-folders {
    min-height: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: 52px;

    &__title {
        white-space: nowrap;
    }

    &__search {
        width: 100%;
    }

    .collection__head {
        gap: 45px;
    }
}

::ng-deep .previous-folders__search {
    .avl-search {
        gap: 5px;

        .avl-search__input {
            &::placeholder {
                color: #96969f;
            }
        }

        mat-icon {
            --icon-color: #96969f;
        }
    }
}
