import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { ProjectsService } from '../services/projects.service';
import { ProjectDetailsQuery } from '../stores/project-details/project-details.query';
import { LoggerService } from '@services';

@Injectable()
export class ProjectDetailsResolver implements Resolve<string | undefined> {

    constructor(
        private readonly projectsService: ProjectsService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly log: LoggerService,
    ) {
    }

    public async resolve(route: ActivatedRouteSnapshot): Promise<string | undefined> {
        const projectId = route.queryParams.fid || this.projectDetailsQuery.projectId;

        if (!projectId) {
            return;
        }

        const currentProjectId = this.projectDetailsQuery.projectId;
        const isDetailsExist = this.projectDetailsQuery.getIsDetailsExist();

        if (currentProjectId !== projectId || !isDetailsExist) {
            const fullPath = this.getFullRoutePath(route);
            const appPath = this.projectsService.parseAppPathFromUrl(fullPath);

            await this.projectsService.loadDetails(projectId, appPath)
                .catch((error) => {
                    this.log.error(error);
                });
        }

        if (!route.queryParams.fid) {
            this.projectsService.addProjectIdToUrl();
        }

        return projectId;
    }

    private getFullRoutePath(snapshot: ActivatedRouteSnapshot): string {
        const pathSegments: string[] = [];

        let current: ActivatedRouteSnapshot | null = snapshot;
        while (current) {
            const path = current.routeConfig?.path;
            if (path) {
                pathSegments.unshift(path);
            }
            current = current.parent;
        }

        return '/' + pathSegments.join('/');
    }

}
