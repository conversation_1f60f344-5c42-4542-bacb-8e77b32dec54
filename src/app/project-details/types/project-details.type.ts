import { Project } from './project.type';

export type ProjectDetails = {
    projectName: string;
    matterNumber: string;
    isAlps: boolean;
    isPrivate: boolean;
    members: string[];
    projectLifeDuration: number;
    alpsClientEmail: string | null;
    alpsNote: string | null;
    found: boolean;
}

export function mapProjectToProjectDetails(project: Project): ProjectDetails {
    return {
        projectName: project.projectName,
        matterNumber: project.matterNumber,
        isAlps: project.isAlps,
        isPrivate: project.isPrivate,
        members: project.members,
        projectLifeDuration: project.projectLifeDuration,
        alpsClientEmail: project.alpsClientEmail,
        alpsNote: project.alpsNote,
        found: project.found,
    };
}
