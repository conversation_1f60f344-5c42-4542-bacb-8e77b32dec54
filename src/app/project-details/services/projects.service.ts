import { Injectable } from '@angular/core';
import { createInitialDetails, ProjectDetailsStore } from '../stores/project-details/project-details.store';
import { ProjectDetailsQuery } from '../stores/project-details/project-details.query';
import { ProjectDetails } from '../types/project-details.type';
import { ProjectApi } from '../api/project.api';
import { projectAppPathMappings, ProjectAppPath } from '../enums/project-app-type.enum';
import { Project } from '../types/project.type';
import { UrlParamsService } from '@services';
import { Router } from '@angular/router';

@Injectable()
export class ProjectsService {

    constructor(
        private readonly store: ProjectDetailsStore,
        private readonly query: ProjectDetailsQuery,
        private readonly api: ProjectApi,
        private readonly urlParamsService: UrlParamsService,
        private readonly router: Router,
    ) {
    }

    public getActiveApp(): ProjectAppPath | null {
        const url = this.router.url;

        return this.parseAppPathFromUrl(url);
    }

    public parseAppPathFromUrl(url: string): ProjectAppPath | null {
        if (!url) {
            return null;
        }

        for (const [path, appPath] of Object.entries(projectAppPathMappings)) {
            if (url.startsWith(path)) {
                return appPath;
            }
        }

        return null;
    }

    public setProjectId(id: string): void {
        this.reset();
        this.store.update({ id });
    }

    public addProjectIdToUrl(): void {
        const projectId = this.query.projectId;

        if (!projectId) {
            return;
        }

        this.urlParamsService.addParams({ fid: projectId });
    }

    public removeProjectIdFromUrl(): void {
        this.urlParamsService.removeParams(['fid']);
    }

    public reset(): void {
        this.store.reset();
        this.removeProjectIdFromUrl();
    }

    public upsertDetails(details: ProjectDetails): Promise<Project> {
        const projectId = this.query.getValue().id;

        if (projectId) {
            return this.updateDetails({ id: projectId, ...details });
        } else {
            return this.createProject(details);
        }
    }

    public createProject(details?: ProjectDetails): Promise<Project> {
        const path = this.getActiveApp();

        return new Promise((resolve, reject) => {
            this.api.create(path)
                .subscribe({
                    next: (id) => {
                        const projectDetails = details || createInitialDetails();
                        this.store.update({ id, ...projectDetails });

                        if (details?.projectName || details?.matterNumber) {
                            this.updateDetails({ id, ...details })
                                .then((project) => resolve(project))
                                .catch((error) => reject(error));
                        } else {
                            resolve({ id, ...projectDetails });
                        }
                    },
                    error: (error) => reject(error),
                });
        });
    }

    public updateDetails(project: Project): Promise<Project> {
        const path = this.getActiveApp();

        return new Promise((resolve, reject) => {
            this.api.updateDetails(path, project.id, project)
                .subscribe({
                    next: () => {
                        this.store.update(project);
                        resolve(project);
                    },
                    error: (error) => reject(error),
                });
        });
    }

    public loadDetails(id: string, appPath?: ProjectAppPath): Promise<Project> {
        const path = appPath || this.getActiveApp();

        return new Promise((resolve, reject) => {
            this.api.getDetails(path, id)
                .subscribe({
                    next: (project) => {
                        this.store.update(project);
                        resolve(project);
                    },
                    error: (error) => reject(error),
                });
        });
    }

}
