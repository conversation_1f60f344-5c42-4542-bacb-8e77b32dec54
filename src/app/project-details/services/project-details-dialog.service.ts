import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ProjectDetailsDialogComponent } from '../components/project-details-dialog/project-details-dialog.component';
import { ProjectDetailsDialogOptions } from '../types/project-details-dialog-options.type';
import { ProjectDetailsDialogResponse } from '../types/project-details-dialog-response.type';
import { ProjectDialogResponseType } from '../enums/project-dialog-response-type.enum';
import { createInitialDetails, ProjectDetailsStore } from '../stores/project-details/project-details.store';
import { ProjectsService } from './projects.service';
import { ProjectDetailsResults } from '../types/project-details-results.type';
import { ProjectDetailsComponentOptions } from '../types/project-details-component-options.type';

@Injectable()
export class ProjectDetailsDialogService {
    private dialogRef: MatDialogRef<ProjectDetailsDialogComponent> | null = null;

    constructor(
        private readonly dialog: MatDialog,
        private readonly store: ProjectDetailsStore,
        private readonly projectsService: ProjectsService,
    ) {
    }

    public show(options: ProjectDetailsDialogOptions): Promise<ProjectDetailsResults> {
        if (this.dialogRef) {
            this.hide();
        }

        const componentOptions: ProjectDetailsComponentOptions = {
            data: options.data || createInitialDetails(),
            isReadOnly: options?.isReadOnly ?? false,
            isCloseDisabled: options.isCloseDisabled,
            isClickOutsideCloseEnabled: options.isClickOutsideCloseEnabled,
        };

        this.dialogRef = this.dialog.open(ProjectDetailsDialogComponent, {
            data: componentOptions,
            panelClass: ['project-details-dialog', ...options.panelClass],
            autoFocus: false,
            disableClose: !options.isClickOutsideCloseEnabled,
        });

        this.dialogRef.afterOpened().subscribe(() => options.afterOpened?.());
        this.dialogRef.beforeClosed().subscribe(() => options.beforeClosed?.());

        return new Promise((resolve, reject) => {
            this.dialogRef.afterClosed().subscribe((result: ProjectDetailsDialogResponse) => {
                if (result?.event === ProjectDialogResponseType.confirm) {
                    this.projectsService.upsertDetails(result.data)
                        .then((project) => resolve({ project: project }))
                        .catch((error) => reject(error));
                } else {
                    this.store.update(result?.data);
                    resolve({ isClosedByUser: true, project: this.store.getValue() });
                }
            });
        });
    }

    public hide(): void {
        this.dialogRef.close(ProjectDialogResponseType.cancel);
        this.dialogRef = null;
    }
}
