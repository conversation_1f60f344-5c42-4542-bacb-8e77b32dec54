import { Component, Inject, On<PERSON><PERSON>roy, OnInit, Optional } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { ProfileService } from '@services';
import { ProjectDialogResponseType } from '../../enums/project-dialog-response-type.enum';
import { ProjectDetailsDialogResponse } from '../../types/project-details-dialog-response.type';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { ProjectDetailsComponentOptions } from '../../types/project-details-component-options.type';
import { ProjectFieldName } from '../../enums/project-field-name.enum';

@Component({
    selector: 'avl-project-details-dialog',
    templateUrl: './project-details-dialog.component.html',
    styleUrls: ['./project-details-dialog.component.scss'],
    animations: [
        trigger('expandCollapse', [
            state('collapsed', style({
                height: '0',
                opacity: '0',
                overflow: 'hidden',
                padding: '0',
                margin: '0',
            })),
            state('expanded', style({
                height: '*',
                opacity: '1',
            })),
            transition('collapsed <=> expanded', animate('200ms ease-in-out')),
        ]),
    ],
})
export class ProjectDetailsDialogComponent implements OnInit, OnDestroy {
    public readonly fieldName = ProjectFieldName;

    public folderDetailsForm: FormGroup;
    public isLongTermStorageEnabled$: Observable<boolean>;
    public isTeamworkEnabled$: Observable<boolean>;
    public isAlpsEnabled$: Observable<boolean>;
    public isReadOnly = false;
    public errorMessage = {
        projectName: 'Project Name is required',
        matterNumber: 'Matter Number is required',
        alpsClientEmail: 'Client Email is required',
        alpsClientEmailInvalid: 'Client Email should be a valid email',
    };

    private readonly destroy$ = new Subject<void>();

    constructor(
        @Optional()
        @Inject(MAT_DIALOG_DATA)
        private readonly options: ProjectDetailsComponentOptions,
        private readonly dialogRef: MatDialogRef<ProjectDetailsDialogComponent, ProjectDetailsDialogResponse>,
        private readonly formBuilder: FormBuilder,
        private readonly profileService: ProfileService,
    ) {
        this.isReadOnly = this.options.isReadOnly;
    }

    public ngOnInit(): void {
        this.isLongTermStorageEnabled$ = this.profileService.isLongTermStorageEnabled$.asObservable();
        this.isTeamworkEnabled$ = this.profileService.isTeamworkEnabled$.asObservable();
        this.isAlpsEnabled$ = this.profileService.isAlpsEnabled$.asObservable();
        this.setupForm();
        this.handleEscapePress();
        this.handleAlpsStatusChange();
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    public close(): void {
        const isCloseDisabled = this.options?.isCloseDisabled;

        if (!isCloseDisabled) {
            this.dialogRef.close({ event: ProjectDialogResponseType.cancel });
        }
    }

    public confirm(): void {
        const folderDetails = this.folderDetailsForm.value;
        const response: ProjectDetailsDialogResponse = {
            event: ProjectDialogResponseType.confirm,
            data: {
                projectName: folderDetails.projectName,
                matterNumber: folderDetails.matterNumber,
                isAlps: folderDetails.isAlps,
                isPrivate: !folderDetails.isPublic,
                members: folderDetails.members ?? [],
                projectLifeDuration: folderDetails.projectLifeDuration,
                alpsClientEmail: folderDetails.alpsClientEmail,
                alpsNote: folderDetails.alpsNote,
                found: folderDetails.found,
            },
        };

        this.dialogRef.close(response);
    }

    public getAlpsFieldsState(): string {
        return this.folderDetailsForm.get(ProjectFieldName.isAlps).value ? 'expanded' : 'collapsed';
    }

    public getTeamProjectsState(): string {
        return this.folderDetailsForm.get(ProjectFieldName.isPublic).value ? 'collapsed' : 'expanded';
    }

    public getErrorMessage(): string {
        const isProjectNameDirtyAndInvalid = this.isDirtyAndInvalid(ProjectFieldName.projectName);
        const isMatterNumberDirtyAndInvalid = this.isDirtyAndInvalid(ProjectFieldName.matterNumber);
        const isAlpsClientEmailDirtyAndInvalid = this.isDirtyAndInvalid(ProjectFieldName.alpsClientEmail);

        let errorMessage = '';

        if (isProjectNameDirtyAndInvalid) {
            errorMessage = this.errorMessage.projectName;
        } else if (isMatterNumberDirtyAndInvalid) {
            errorMessage = this.errorMessage.matterNumber;
        } else if (isAlpsClientEmailDirtyAndInvalid) {
            const emailControl = this.folderDetailsForm.get(ProjectFieldName.alpsClientEmail);
            if (emailControl.hasError('required')) {
                errorMessage = this.errorMessage.alpsClientEmail;
            } else if (emailControl.hasError('email')) {
                errorMessage = this.errorMessage.alpsClientEmailInvalid;
            }
        }

        return errorMessage;
    }

    public isDirtyAndInvalid(fieldName: string): boolean {
        const control = this.folderDetailsForm.get(fieldName);

        if (!control) {
            return false;
        }

        return control.dirty && control.invalid;
    }

    private setupForm(): void {
        const data = this.options?.data;
        const planValue = this.profileService.plan$.getValue();
        const folderNamePattern = planValue.projectName.pattern;
        const folderMatterNumberPattern = planValue.matterNumber.pattern;
        const isAlpsEnabled = this.profileService.isAlpsEnabled$.getValue();
        const isTeamworkEnabled = this.profileService.isTeamworkEnabled$.getValue();
        const isLongTermStorageEnabled = this.profileService.isLongTermStorageEnabled$.getValue();

        const nameValidator = [Validators.required];
        const matterNumberValidator = [Validators.required];

        const membersValue = isTeamworkEnabled ? data.members ?? [] : [];
        const isPublicValue = isTeamworkEnabled ? !data.isPrivate ?? false : null;
        const projectLifeDuration = isLongTermStorageEnabled ? data.projectLifeDuration : null;
        const isAlpsValue = isAlpsEnabled ? (data.isAlps || false) : false;
        const alpsNote = isAlpsEnabled ? data.alpsNote : null;
        const alpsClientEmail = isAlpsEnabled ? data.alpsClientEmail : null;

        if (folderNamePattern) {
            nameValidator.push(Validators.pattern(folderNamePattern));
            this.errorMessage.projectName = planValue.projectName.errorMessage;
        }

        if (folderMatterNumberPattern) {
            matterNumberValidator.push(Validators.pattern(folderMatterNumberPattern));
            this.errorMessage.matterNumber = planValue.matterNumber.errorMessage;
        }

        this.folderDetailsForm = this.formBuilder.group({
            [ProjectFieldName.projectName]: [data.projectName, nameValidator],
            [ProjectFieldName.matterNumber]: [data.matterNumber, matterNumberValidator],
            [ProjectFieldName.isPublic]: [isPublicValue],
            [ProjectFieldName.members]: [membersValue],
            [ProjectFieldName.projectLifeDuration]: [projectLifeDuration],
            [ProjectFieldName.isAlps]: [isAlpsValue],
            [ProjectFieldName.alpsClientEmail]: [alpsClientEmail],
            [ProjectFieldName.alpsNote]: [alpsNote],
        });
    }

    private handleAlpsStatusChange(): void {
        this.folderDetailsForm.get(ProjectFieldName.isAlps).valueChanges
            .pipe(takeUntil(this.destroy$))
            .subscribe((isAlps) => {
                const alpsClientEmailControl = this.folderDetailsForm.get(ProjectFieldName.alpsClientEmail);
                const alpsNoteControl = this.folderDetailsForm.get(ProjectFieldName.alpsNote);

                if (isAlps) {
                    alpsClientEmailControl.setValidators([Validators.required, Validators.email]);
                } else {
                    alpsClientEmailControl.clearValidators();
                    alpsNoteControl.clearValidators();
                    alpsClientEmailControl.setValue(null);
                    alpsNoteControl.setValue(null);
                }

                alpsClientEmailControl.updateValueAndValidity();
                alpsNoteControl.updateValueAndValidity();
            });
    }

    private handleEscapePress(): void {
        this.dialogRef.keydownEvents()
            .pipe(takeUntil(this.destroy$))
            .subscribe((event) => {
                if (event.key === 'Escape') {
                    this.close();
                }
            });
    }
}
