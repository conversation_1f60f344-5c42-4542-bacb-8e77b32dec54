<button
    class="dialog__close"
    (click)="close()"
>
    <mat-icon
        class="close-icon"
        svgIcon="close"
    ></mat-icon>
</button>
<h4 mat-dialog-title>Project Settings</h4>
<form
    [formGroup]="folderDetailsForm"
    (submit)="confirm()"
>
    <mat-dialog-content class="sections">
        <div class="section">
            <p class="section__title">Project Details</p>
            <div
                class="form-group"
                [class.has-error]="isDirtyAndInvalid(fieldName.projectName)"
            >
                <input
                    type="text"
                    class="form-control mb-10"
                    placeholder="Project Name"
                    autocomplete="off"
                    [name]="fieldName.projectName"
                    [formControlName]="fieldName.projectName"
                />
                <mat-icon
                    class="form-icon"
                    svgIcon="user"
                ></mat-icon>
            </div>
            <div
                class="form-group"
                [class.has-error]="isDirtyAndInvalid(fieldName.matterNumber)"
            >
                <input
                    type="text"
                    class="form-control"
                    placeholder="Client / Matter Number"
                    autocomplete="off"
                    [name]="fieldName.matterNumber"
                    [formControlName]="fieldName.matterNumber"
                />
                <mat-icon
                    class="form-icon"
                    svgIcon="file-chart-rounded"
                ></mat-icon>
            </div>
        </div>

        <ng-container *ngIf="isTeamworkEnabled$ | async">
            <div class="section">
                <p class="section__title">Teamwork <span class="info-tooltip"></span></p>
                <div class="form-group checkbox">
                    <mat-checkbox
                        class="checkbox__input"
                        [formControlName]="fieldName.isPublic"
                    >
                        Allow everyone in your organisation to access
                    </mat-checkbox>
                </div>
                <div
                    [@expandCollapse]="getTeamProjectsState()"
                    class="teamwork-emails"
                >
                    <div
                        class="form-group"
                        [class.has-error]="isDirtyAndInvalid(fieldName.members)"
                    >
                        <avl-email-chips-field
                            placeholder="Emails of people for teamwork"
                            [formControlName]="fieldName.members"
                        ></avl-email-chips-field>
                    </div>
                </div>
            </div>
        </ng-container>

        <ng-container *ngIf="isLongTermStorageEnabled$ | async">
            <div class="section">
                <p class="section__title">Document Retention (days)</p>
                <avl-slider
                    [formControlName]="fieldName.projectLifeDuration"
                    [isDisabled]="false"
                    [max]="730"
                    [min]="1"
                    [step]="1"
                    [value]="90"
                    [isThumbLabelVisible]="true"
                ></avl-slider>
            </div>
        </ng-container>

        <ng-container *ngIf="isAlpsEnabled$ | async">
            <div class="section">
                <div class="form-group checkbox">
                    <mat-checkbox
                        formControlName="isAlps"
                        class="checkbox__input"
                    >
                        Enable ALPS for this project
                    </mat-checkbox>
                </div>

                <div
                    [@expandCollapse]="getAlpsFieldsState()"
                    class="alps-fields"
                >

                    <div
                        class="form-group"
                        [class.has-error]="isDirtyAndInvalid(fieldName.alpsClientEmail)"
                    >
                        <input
                            type="email"
                            class="form-control mb-10"
                            placeholder="ALPS Client Email"
                            autocomplete="off"
                            [name]="fieldName.alpsClientEmail"
                            [formControlName]="fieldName.alpsClientEmail"
                        />
                        <mat-icon
                            class="form-icon email-icon"
                            svgIcon="alternate-email"
                        ></mat-icon>
                    </div>
                    <div
                        class="form-group"
                        [class.has-error]="isDirtyAndInvalid(fieldName.alpsNote)"
                    >
                        <input
                            type="text"
                            class="form-control"
                            placeholder="ALPS Note"
                            autocomplete="off"
                            [name]="fieldName.alpsNote"
                            [formControlName]="fieldName.alpsNote"
                        />
                        <mat-icon
                            class="form-icon note-icon"
                            svgIcon="mist"
                        ></mat-icon>
                    </div>
                </div>
            </div>
        </ng-container>

        <div
            class="project-details-dialog__error-message"
            [class.has-error]="!!getErrorMessage()"
        >
            <p>{{ getErrorMessage() }}</p>
        </div>
    </mat-dialog-content>
    <div mat-dialog-actions>
        <button
            class="avl-btn avl-btn--dark-purple avl-btn--wide"
            [disabled]="folderDetailsForm.invalid"
        >
            Let’s Go
        </button>
    </div>
</form>
