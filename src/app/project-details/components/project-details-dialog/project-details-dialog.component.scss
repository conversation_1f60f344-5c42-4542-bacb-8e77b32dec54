@import '~assets/sass/variables';

::ng-deep .project-details-dialog {
    width: 446px;

    .mat-dialog-title {
        margin: 0;
        text-align: center;
    }

    .mat-dialog-container {
        position: relative;
        padding: 0;
    }

    .mat-dialog-content {
        padding: 25px 42px 0;
        min-height: 230px;
    }

    .form-control {
        font-size: 14px;
    }

    .form-group .form-icon {
        left: 20px;
        opacity: 1;
    }

    .mat-dialog-actions {
        padding: 0;

        .avl-btn {
            border-radius: 0;
            padding-top: 21px;
            line-height: 18px;
            padding-bottom: 21px;

            &--dark-purple {
                &:disabled {
                    background-color: $light-gray;
                }
            }
        }
    }

    &__error-message {
        opacity: 0;
        min-height: 16px;
        margin-bottom: 10px;
        color: $bright-red;
        font-size: 12px;
        line-height: 16px;
        transition: opacity 0.2s ease;

        &.has-error {
            opacity: 1;
        }
    }

    .form-group {
        margin-bottom: initial;

        &.has-error {
            .form-control {
                box-shadow: inset 0 0 0 1px $bright-red;
                border-color: $bright-red;
            }
        }

        &::after {
            top: 20px;
            right: 15px;
            width: 22px;
            height: 22px;
            background: url('~assets/icons/icon_alert_error.svg') no-repeat center;
        }

        .error-message {
            color: $bright-red;
        }
    }

    .checkbox {
        &__input {
            font-size: 14px;
        }
    }

    .alps-fields {
        overflow: hidden;
    }
}

.mb-10 {
    margin-bottom: 10px;
}

.sections {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.section {
    display: flex;
    flex-direction: column;
    gap: 5px;

    &__title {
        font-size: 14px;
        line-height: 18px;
        font-weight: 700;
        color: $blue;
        text-transform: capitalize;
    }
}

.email-icon,
.note-icon {
    width: 20px;
    height: 20px;
    color: $light-gray;
}
