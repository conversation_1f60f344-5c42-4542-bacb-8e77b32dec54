import { Injectable } from '@angular/core';
import { Store, StoreConfig } from '@datorama/akita';
import { Project } from '../../types/project.type';
import { ProjectDetails } from '../../types/project-details.type';

export type ProjectDetailsState = Project & {
    hasFullAccess: boolean;
};

export function createInitialDetails(): ProjectDetails {
    return {
        projectName: '',
        matterNumber: '',
        isAlps: false,
        isPrivate: true,
        members: [],
        projectLifeDuration: 90,
        alpsClientEmail: null,
        alpsNote: null,
        found: false,
    };
}

export function createInitialState(): ProjectDetailsState {
    return {
        id: '',
        hasFullAccess: false,
        ...createInitialDetails(),
    };
}

@Injectable()
@StoreConfig({ name: 'project-details', resettable: true })
export class ProjectDetailsStore extends Store<ProjectDetailsState> {

    constructor() {
        super(createInitialState());
    }

}
