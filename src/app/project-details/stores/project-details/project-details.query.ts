import { Injectable } from '@angular/core';
import { Query } from '@datorama/akita';
import { ProjectDetailsState, ProjectDetailsStore } from './project-details.store';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { mapProjectToProjectDetails, ProjectDetails } from '../../types/project-details.type';

@Injectable()
export class ProjectDetailsQuery extends Query<ProjectDetailsState> {
    public projectId$ = this.select('id');

    constructor(protected store: ProjectDetailsStore) {
        super(store);
    }

    public get projectId(): string {
        return this.getValue().id;
    }

    public get isProjectCreated(): boolean {
        return !!this.getValue().id;
    }

    public get isUserHasFullAccess(): boolean {
        return this.getValue().hasFullAccess;
    }

    public selectProjectId(): Observable<string> {
        return this.select('id');
    }

    public getDetails(): ProjectDetails {
        const details = this.getValue();

        return mapProjectToProjectDetails(details);
    }

    public selectDetails(): Observable<ProjectDetails> {
        return this.select()
            .pipe(
                map(mapProjectToProjectDetails),
            );
    }

    public getIsDetailsExist(): boolean {
        const details = this.getDetails();

        return details.projectName !== '' && details.matterNumber !== '';
    }
}
