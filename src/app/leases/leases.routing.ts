import { Routes } from '@angular/router';
import { LeasesUploadComponent } from './components/leases-upload/leases-upload.component';
import { LeasesResolver } from './resolvers/leases.resolver';
import { PreviousFoldersComponent } from './components/previous-folders/previous-folders.component';
import { LeasesComponent } from './leases.component';
import { DocumentViewerComponent } from '@shared/components/document-viewer/document-viewer.component';
import { NotFoundPageComponent } from '@shared/components/not-found-page/not-found-page.component';
import { ProjectDetailsResolver } from '../project-details/resolvers/project-details.resolver';

export const leasesRoutes: Routes = [
    {
        path: '',
        component: LeasesComponent,
        children: [
            {
                path: '',
                redirectTo: 'upload',
                pathMatch: 'full',
            },
            {
                path: 'upload',
                component: LeasesUploadComponent,
                resolve: {
                    project: ProjectDetailsResolver,
                    status: LeasesResolver,
                },
            },
            {
                path: 'previous-projects',
                component: PreviousFoldersComponent,
            },
        ],
    },
    {
        path: 'documents',
        children: [
            {
                path: 'not-found',
                component: NotFoundPageComponent,
                data: {
                    logoUrl: '/assets/images/avail-lease-logo.svg',
                    title: 'Document Not Found',
                    description: 'This document has been deleted or moved.<br>Click Back to Project to return to your project or the home page.<br>Alternatively, select Message Us to contact our support team for assistance.',
                    backButtonText: 'Back to Project',
                },
            },
            {
                path: ':documentId',
                data: {
                    resourceUrlWithPlaceholders: 'api/filed-documents/folder/:folderId/document/:documentId/preview?kind=ocr',
                    resourceNotFoundPage: 'lease/documents/not-found',
                    folderPageUrl: '/lease/upload?fid=:folderId',
                },
                component: DocumentViewerComponent,
            },
        ],
    },
];
