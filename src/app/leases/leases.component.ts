import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewChild } from '@angular/core';
import { Observable } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ConfirmDialogComponent } from '@shared/components/dialogs/confirm-dialog/confirm-dialog.component';
import { CREATE_NEW_FOLDER_CONFIRM_DATA } from '@constants';
import { filter } from 'rxjs/operators';
import { CompileService, SideNavCountersQuery, SideNavCountersService } from './states';
import { MatSidenav } from '@angular/material/sidenav';
import { Logo } from '@core/types';
import { AuthService } from '@auth/services';
import { NotificationService } from './components/notification-center/services/notification.service';
import { environment } from '@env/environment';
import { resetStores } from '@datorama/akita';
import { HttpQueryParamsService, LoggerService, ProfileService } from '@services';
import { SideNavCounters } from './types/side-nav-counters.type';
import { LeaseService } from './services/lease.service';
import { ProjectDetailsDialogOptions } from '../project-details/types/project-details-dialog-options.type';
import { leaseProjectDetailsOptions } from './constants/lease-project-details-dialog-options.constant';
import { ProjectDetailsDialogService } from '../project-details/services/project-details-dialog.service';
import { ProjectDetailsQuery } from '../project-details/stores/project-details/project-details.query';
import { ProjectDetails } from '../project-details/types/project-details.type';


@Component({
    selector: 'avl-leases',
    templateUrl: './leases.component.html',
    styleUrls: ['./leases.component.scss'],
})
export class LeasesComponent implements OnInit, OnDestroy {

    public readonly leaseLogo: Logo = {
        defaultLogo: true,
        icon: 'assets/images/avail-lease-logo-beta.svg',
        iconWidth: 169,
        iconHeight: 20,
    };

    @ViewChild(MatSidenav, { static: false })
    public sidenavComponent: MatSidenav;

    public folderIsNotCreated$: Observable<boolean>;
    public isBellHighlighted$: Observable<boolean>;
    public folderDetails$: Observable<ProjectDetails>;
    public counters$: Observable<SideNavCounters>;

    public profileIconReference: Element;

    constructor(
        private readonly authService: AuthService,
        private readonly profileService: ProfileService,
        private readonly dialog: MatDialog,
        private readonly router: Router,
        private readonly notificationService: NotificationService,
        private readonly queryParamsService: HttpQueryParamsService,
        private readonly countersQuery: SideNavCountersQuery,
        private readonly sideNavCountersService: SideNavCountersService,
        private readonly compileService: CompileService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly projectDetailsDialogService: ProjectDetailsDialogService,
        private readonly leaseService: LeaseService,
        private readonly log: LoggerService,
    ) {
    }

    public ngOnInit(): void {
        this.isBellHighlighted$ = this.notificationService.isBellHighlighted();
        this.compileService.checkFinish();
        this.counters$ = this.countersQuery.select();
        this.folderDetails$ = this.projectDetailsQuery.selectDetails();
        this.folderIsNotCreated$ = this.projectDetailsQuery.select((state) => !state.id);
        this.profileIconReference = document.getElementById('profile');
        this.profileService.loadConfig();
        this.loadCounters();
    }

    public ngOnDestroy(): void {
        resetStores({ exclude: ['project-details'] });
    }

    public async onNewFolderCreated(): Promise<void> {
        const isProjectCreated = this.projectDetailsQuery.isProjectCreated;

        if (isProjectCreated) {
            const config = {
                panelClass: 'confirm-dialog',
                data: { ...CREATE_NEW_FOLDER_CONFIRM_DATA, color: 'dark-purple' },
            };

            this.dialog.open(ConfirmDialogComponent, config)
                .afterClosed()
                .pipe(
                    filter((isConfirm) => isConfirm),
                )
                .subscribe(async () => await this.leaseService.resetProject());
        } else {
            await this.leaseService.resetProject();
        }

        await this.sidenavComponent?.close();
    }

    public async logout(): Promise<void> {
        const queryParams = this.queryParamsService.addRedirectToQueryParams({ isOtherParamsIncluded: false });

        await this.authService.logout();
        void this.router.navigate(['/login'], { queryParams });
    }

    public onFolderDetailsOpen(): void {
        const folderDetails = this.projectDetailsQuery.getDetails();
        const isUserHasFullAccess = this.projectDetailsQuery.isUserHasFullAccess;
        const options: ProjectDetailsDialogOptions = {
            ...leaseProjectDetailsOptions,
            data: folderDetails,
            isReadOnly: !isUserHasFullAccess,
        };

        void this.projectDetailsDialogService.show(options)
            .catch((error) => this.log.error(error));
    }

    public onNotificationCenterOpened(): void {
        const folderId = this.projectDetailsQuery.projectId;
        this.notificationService.showDialog(folderId);
    }

    public isProviderSso(): boolean {
        return this.authService.isProviderSso();
    }

    public async logoutStay(): Promise<void> {
        await this.authService.logout();
    }

    public isEnvironmentProduction(): boolean {
        return environment.production;
    }

    public loadCounters(): void {
        this.sideNavCountersService.load();
    }
}
