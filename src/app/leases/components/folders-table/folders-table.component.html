<div class="collection__table-container folders-table">
    <div class="collection__table-wrap">
        <table
            class="collection__table"
            mat-table
            matSort
            matSortDisableClear
            [trackBy]="trackReports"
            [dataSource]="source"
        >
            <!-- Project Name Column -->
            <ng-container matColumnDef="project-name">
                <th
                    mat-header-cell
                    mat-sort-header="projectName"
                    *matHeaderCellDef
                    class="folders-table__header-th"
                >
                    Project Name
                </th>
                <td
                    mat-cell
                    *matCellDef="let element"
                    class="folders-table__row"
                >
                    {{ element.projectName || '-' }}
                </td>
            </ng-container>

            <!-- Client/Matter Column -->
            <ng-container matColumnDef="matter-number">
                <th
                    mat-header-cell
                    mat-sort-header="matterNumber"
                    *matHeaderCellDef
                    class="folders-table__header-th"
                >
                    Client&nbsp;/ Matter
                </th>
                <td
                    mat-cell
                    *matCellDef="let element"
                    class="folders-table__row"
                >
                    {{ element.matterNumber || '-' }}
                </td>
            </ng-container>

            <!-- Created Column -->
            <ng-container matColumnDef="created-at">
                <th
                    mat-header-cell
                    mat-sort-header="createdAt"
                    *matHeaderCellDef
                    class="folders-table__header-th"
                    [class.sortable]="sort.active === 'createdAt'"
                >
                    Created
                </th>
                <td
                    mat-cell
                    *matCellDef="let element"
                    [class.sortable]="sort.active === 'createdAt'"
                    class="folders-table__row"
                >
                    {{ (element.createdAt | date:'d MMMM y') || '-' }}
                </td>
            </ng-container>

            <!-- Last Updated Column -->
            <ng-container matColumnDef="last-opened-at">
                <th
                    mat-header-cell
                    mat-sort-header="lastOpenedAt"
                    *matHeaderCellDef
                    class="folders-table__header-th"
                    [class.sortable]="sort.active === 'lastOpenedAt'"
                >
                    Last Updated
                </th>
                <td
                    mat-cell
                    *matCellDef="let element"
                    [class.sortable]="sort.active === 'lastOpenedAt'"
                    class="folders-table__row"
                >
                    {{ (element.lastOpenedAt | date:'d MMMM y') || '-' }}
                </td>
            </ng-container>

            <!-- Leases Column -->
            <ng-container matColumnDef="leases">
                <th
                    mat-header-cell
                    mat-sort-header=""
                    *matHeaderCellDef
                    class="folders-table__header-th folders-table__header-th--center"
                    [class.sortable]="sort.active === 'leasesCount'"
                >
                    Leases
                </th>
                <td
                    mat-cell
                    *matCellDef="let element"
                    [class.sortable]="sort.active === 'leasesCount'"
                    class="folders-table__row folders-table__cell--center"
                >
                    {{ element.leasesCount || '-' }}
                </td>
            </ng-container>

            <ng-container matColumnDef="download">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    class="folders-table__header-th"
                ></th>
                <td
                    mat-cell
                    *matCellDef="let element"
                >
                    <div
                        [matTooltip]="getTooltipMessage(element)"
                        [matTooltipDisabled]="isBookmarkReady(element)"
                        matTooltipClass="mat-tooltip__bright-without-opacity"
                        matTooltipPosition="right"
                    >
                        <button
                            class="folders-table__row folders-table__download-btn avl-btn avl-btn--dark-blue"
                            [disabled]="!isBookmarkReady(element)"
                            (click)="downloadReport(element)"
                        >
                            {{ getDownloadButtonLabel(element) }}
                        </button>
                    </div>
                </td>
            </ng-container>

            <ng-container matColumnDef="open">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                    class="folders-table__header-th"
                ></th>
                <td
                    mat-cell
                    *matCellDef="let element"
                >
                    <button
                        class="folders-table__open-folder-btn avl-btn avl-btn--dark-purple"
                        (click)="openFolder(element)"
                    >
                        Open
                    </button>
                </td>
            </ng-container>

            <tr
                mat-header-row
                *matHeaderRowDef="displayedColumns"
            ></tr>
            <tr
                mat-row
                [hidden]="loading"
                *matRowDef="let row; columns: displayedColumns;"
            ></tr>
        </table>
        <avl-table-loading-placeholder
            *ngIf="loading"
            [columns]="displayedColumns"
            [size]="pageSize"
        ></avl-table-loading-placeholder>
    </div>

    <avl-table-no-data-disclaimer
        *ngIf="!loading && !bookmarks.length"
        [iconName]="'file-chart-rounded'"
        [message]="'No previous projects found'"
    ></avl-table-no-data-disclaimer>
    <mat-paginator
        [length]="pagination.totalCount"
        [class.hidden]="!bookmarks.length"
        [pageSize]="pageSize"
        [hidePageSize]="true"
    ></mat-paginator>
</div>
