@import 'src/assets/sass/variables';

.sidenav {
    &__head-bg {
        width: 304px;
    }

    &__content {
        display: flex;
        flex-direction: column;
        height: 100%;
        padding-top: 0;
    }

    &__buttons {
        height: 100%;
        padding: 0 8px;
        font-weight: 500;
    }

    &__list-item {
        margin: 8px 0;
        border-radius: 4px;
        background-color: #fff;
        transition: all 0.2s linear;
        cursor: pointer;

        &:hover {
            box-shadow: 0 4px 8px rgba(48, 79, 254, 0.04);
            background-color: rgba($dark-purple, 0.24);
        }

        &.mat-list-item {
            font-size: 14px;
            line-height: 24px;
            height: 48px;

            .mat-list-item-content {
                padding: 0 12px;
            }
        }

        .mat-icon {
            margin-right: 35px;
        }
    }

    &__button-icon {
        --icon-color: #{$dark-purple};
        --icon-stroke-width: 2;
        width: 20px;
        height: 20px;
    }

    &__apps {
        height: 150px;
    }
}

.position-relative {
    position: relative;
}

.counter-box {
    position: absolute;
    top: 12px;
    right: 12px;
    min-width: 24px;
    padding: 0 3px;
    height: 24px;
    background-color: $dark-purple;
    color: #fff;
    line-height: 24px;
    text-align: center;
    border-radius: 3px;
}

::ng-deep .mat-list-base .mat-list-item .mat-list-item-content {
    padding: 0 10px !important;
}
