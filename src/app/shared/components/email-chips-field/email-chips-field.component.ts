import { COMM<PERSON>, ENT<PERSON>, SEMICOLON, SPACE } from '@angular/cdk/keycodes';
import { Component, forwardRef, Input } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatChipInputEvent } from '@angular/material/chips';

@Component({
    selector: 'avl-email-chips-field',
    templateUrl: './email-chips-field.component.html',
    styleUrls: ['./email-chips-field.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => EmailChipsFieldComponent),
            multi: true,
        },
    ],
})
export class EmailChipsFieldComponent implements ControlValueAccessor {
    public readonly separatorKeysCodes = [ENTER, COMMA, SEMICOLON, SPACE] as const;

    @Input()
    public placeholder = 'Enter email';

    @Input()
    public label = '';

    @Input()
    public required = false;

    public addOnBlur = true;
    public emails: string[] = [];
    public isDisabled = false;

    public add(event: MatChipInputEvent): void {
        const value = (event.value || '').trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (value && emailRegex.test(value) && !this.emails.includes(value)) {
            this.emails.push(value);
            this.onChange(this.emails);
        }

        event.chipInput.clear();
    }

    public remove(email: string): void {
        const index = this.emails.indexOf(email);

        if (index >= 0) {
            this.emails.splice(index, 1);
            this.onChange(this.emails);
        }
    }

    // ControlValueAccessor implementation
    public writeValue(emails: string[]): void {
        this.emails = emails || [];
    }

    public registerOnChange(fn: (emails: string[]) => void): void {
        this.onChange = fn;
    }

    public registerOnTouched(fn: () => void): void {
        this.onTouched = fn;
    }

    public setDisabledState(isDisabled: boolean): void {
        this.isDisabled = isDisabled;
    }

    private onChange: (emails: string[]) => void = () => {
        // No implementation
    };

    private onTouched: () => void = () => {
        // No implementation
    };
}
