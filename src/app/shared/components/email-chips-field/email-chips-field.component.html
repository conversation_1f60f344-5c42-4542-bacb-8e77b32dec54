<div class="email-field-container">
    <mat-icon
        class="form-icon email-icon"
        svgIcon="alternate-email"
    ></mat-icon>
    <mat-form-field
        class="email-chips-field"
        appearance="outline"
    >
        <mat-label *ngIf="label">{{ label }}</mat-label>
        <mat-chip-list
            #chipList
            aria-label="Email addresses"
        >
            <mat-chip
                *ngFor="let email of emails"
                [removable]="!isDisabled"
                (removed)="remove(email)"
            >
                {{ email }}
                <button
                    matChipRemove
                    *ngIf="!isDisabled"
                >
                    <mat-icon
                        svgIcon="close"
                    ></mat-icon>
                </button>
            </mat-chip>
            <input
                [placeholder]="placeholder"
                [matChipInputFor]="chipList"
                [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                [matChipInputAddOnBlur]="addOnBlur"
                [disabled]="isDisabled"
                (matChipInputTokenEnd)="add($event)"
                (blur)="onTouched()"
                aria-label="Email input"
                autocomplete="off"
                type="email"
            >
        </mat-chip-list>
    </mat-form-field>
</div>
