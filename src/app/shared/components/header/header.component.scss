@import '~assets/sass/variables';

.header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;

    &__menu {
        opacity: 0.5;
        transition: opacity 0.2s ease;

        &.mat-icon-button {
            margin-right: 16px;
        }

        &:hover {
            opacity: 1;
        }
    }

    &__logo {
        margin-right: auto;
    }

    &__details {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        color: $dark-gray;
        opacity: 0.5;
        font-size: 12px;
        font-family: 'ArialBold';
        line-height: 16px;
        max-width: 60%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-transform: uppercase;
        cursor: pointer;
        transition: all 0.2s linear;

        &:hover {
            opacity: 1;
        }
    }

    &__nav {
        display: flex;
        align-items: center;
    }

    &__nav-btn-wrap {
        width: 40px;
        height: 40px;
        margin: 0 4px;
        padding: 8px;
        border-radius: 50%;
    }

    &__nav-btn {
        width: 24px;
        height: 24px;
        border: none;
        cursor: pointer;
        background-color: transparent;
        transition: opacity 0.2s ease;

        &:disabled {
            cursor: default;
            opacity: 0.5;
        }
    }

    &__explanation-triangle {
        --icon-color: #c80707;
        position: absolute;
        top: -5px;
        right: -4px;
        width: 16px !important;
        height: 16px !important;
        cursor: pointer;
    }
}

.opacity {
    opacity: 0.5;

    &:hover {
        opacity: 1;
    }
}

.relative {
    position: relative;
}
