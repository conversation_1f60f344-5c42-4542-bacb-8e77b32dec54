.slider-wrapper {
    display: flex;
    flex-direction: column;
    gap: 8px;

    --primary-color: #748ece;
    --secondary-color: #d9d9d9;
    --thumb-height: 12px;
    --thumb-background-color: white;
    --line-height: 4px;
}

.slider {
    &__values {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        color: black;
    }

    &__min-value,
    &__max-value {
        opacity: 0.5;
    }

    &__current-value {
        color: var(--primary-color);
    }

    ::ng-deep {
        & .mat-slider-track-background {
            background-color: var(--secondary-color);
        }

        & .mat-slider-track-fill {
            background-color: var(--primary-color);
        }

        & .mat-slider-thumb {
            background-color: var(--thumb-background-color) !important;
            border: 2px solid var(--primary-color);
            height: var(--thumb-height);
            width: var(--thumb-height);
            transform: none;
            bottom: calc(var(--thumb-height) * -1 / 2);
            right: calc(var(--thumb-height) * -1 / 2);
        }

        & .mat-slider-thumb-label {
            background-color: var(--primary-color);
        }

        & .mat-slider-tick {
            background-color: var(--secondary-color);
        }
    }
}

::ng-deep .slider {
    &.mat-slider-horizontal {
        height: var(--thumb-height);
        padding: 0;

        & .mat-slider-wrapper {
            top: calc(var(--thumb-height) / 2);
            height: var(--line-height);
            left: 0;
            right: 0;
        }

        & .mat-slider-track-wrapper {
            height: var(--line-height);
            border-radius: var(--line-height);

            & .mat-slider-track-background {
                height: var(--line-height);
            }

            & .mat-slider-track-fill {
                height: var(--line-height);
            }
        }
    }
}
