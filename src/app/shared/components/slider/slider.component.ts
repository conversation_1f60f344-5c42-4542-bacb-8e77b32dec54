import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatSliderChange } from '@angular/material/slider';

@Component({
    selector: 'avl-slider',
    templateUrl: './slider.component.html',
    styleUrls: ['./slider.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => SliderComponent),
            multi: true,
        },
    ],
})
export class SliderComponent implements OnInit, ControlValueAccessor {
    @Input()
    public max = 100;

    @Input()
    public min = 0;

    @Input()
    public step = 1;

    @Input()
    public value = 10;

    @Input()
    public isDisabled = false;

    @Input()
    public isThumbLabelVisible = false;

    public autoTicks = false;
    public showTicks = false;
    public tickInterval = 1;
    public currentValue = 0;


    public ngOnInit(): void {
        this.currentValue = this.value;
    }

    public writeValue(value: number): void {
        if (value !== undefined && value !== null) {
            this.value = value;
            this.currentValue = value;
        }
    }

    public registerOnChange(fn: (value: number) => void): void {
        this.onChangeFn = fn;
    }

    public registerOnTouched(fn: () => void): void {
        this.onTouchedFn = fn;
    }

    public setDisabledState(isDisabled: boolean): void {
        this.isDisabled = isDisabled;
    }

    public getSliderTickInterval(): number | 'auto' {
        if (this.showTicks) {
            return this.autoTicks ? 'auto' : this.tickInterval;
        }

        return 0;
    }

    public onChange(value: number | null): void {
        if (value !== null) {
            this.value = value;
            this.onChangeFn(value);
        }
    }

    public onInput(event: MatSliderChange): void {
        if (event.value !== null) {
            this.currentValue = event.value;
            this.onTouchedFn();
        }
    }

    private onChangeFn: (value: number) => void = () => {
        // No implementation
    };

    private onTouchedFn: () => void = () => {
        // No implementation
    };
}
