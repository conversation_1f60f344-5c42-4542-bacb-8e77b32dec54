<div class="slider-wrapper">
    <mat-slider
        class="slider"
        [disabled]="isDisabled"
        [max]="max"
        [min]="min"
        [step]="step"
        [thumbLabel]="isThumbLabelVisible"
        [tickInterval]="getSliderTickInterval()"
        [value]="value"
        (change)="onChange($event.value)"
        (input)="onInput($event)"
    ></mat-slider>
    <div class="slider__values">
        <span class="slider__min-value">{{ min }}</span>
        <span class="slider__current-value">{{ currentValue }}</span>
        <span class="slider__max-value">{{ max }}</span>
    </div>
</div>
