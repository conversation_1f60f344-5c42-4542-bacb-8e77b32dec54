import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { <PERSON>ttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';

import { environment } from '@env/environment';


@Injectable()
export class APIInterceptor implements HttpInterceptor {
    public intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
        const bypass = 'assets';

        if (request.url.includes(bypass)) {
            return next.handle(request);
        }

        const userRequest = request.clone({ url: `${environment.api}/${request.url}` });

        return next.handle(userRequest);
    }
}
